package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const apiKey = ""
const username = ""
const tenant = ""
const testLoadID = ""

func TestLiveStarkGetLoad(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkGetLoad: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	var load models.Load
	var attrs models.LoadAttributes
	t.Run("GetLoad", func(t *testing.T) {
		log.Info(ctx, "calling GetLoad for test shipment ID")

		startTime := time.Now()
		var err error
		load, attrs, err = client.GetLoad(ctx, testLoadID)
		require.NoError(t, err, "GetLoad should succeed")

		log.Info(ctx, "GetLoad completed",
			zap.Any("load", load))

		// Verify basic load structure
		assert.NotEmpty(t, load.ExternalTMSID, "load should have external TMS ID")
		assert.NotEmpty(t, load.FreightTrackingID, "load should have freight tracking ID")
		assert.Equal(t, testLoadID, load.ExternalTMSID, "external TMS ID should match requested ID")

		log.Info(ctx, "GetLoad completed",
			zap.String("externalTMSID", load.ExternalTMSID),
			zap.String("freightTrackingID", load.FreightTrackingID),
			zap.String("status", load.Status),
			zap.String("customerName", load.Customer.Name),
			zap.Int("stopsCount", len(load.Stops)),
			zap.Int("notesCount", len(load.Notes)),
			zap.Duration("duration", time.Since(startTime)))
	})

	t.Run("ValidateBasicLoadInfo", func(t *testing.T) {
		assert.NotEmpty(t, load.ExternalTMSID, "load should have external TMS ID")
		assert.NotEmpty(t, load.FreightTrackingID, "load should have freight tracking ID")
		assert.NotEmpty(t, load.Status, "load should have status")
		assert.Equal(t, uint(1), load.ServiceID, "service ID should be set")

		log.Info(ctx, "basic load info validated",
			zap.String("externalTMSID", load.ExternalTMSID),
			zap.String("freightTrackingID", load.FreightTrackingID),
			zap.String("status", load.Status))
	})

	t.Run("ValidatePickupInfo", func(t *testing.T) {
		if load.Pickup.City == "" {
			t.Skip("skipping pickup validation: no pickup data found")
			return
		}

		assert.NotEmpty(t, load.Pickup.City, "pickup should have city")
		assert.NotEmpty(t, load.Pickup.State, "pickup should have state")

		log.Info(ctx, "pickup info validated",
			zap.String("city", load.Pickup.City),
			zap.String("state", load.Pickup.State),
			zap.String("zipcode", load.Pickup.Zipcode),
			zap.String("timezone", load.Pickup.Timezone))
	})

	t.Run("ValidateConsigneeInfo", func(t *testing.T) {
		if load.Consignee.City == "" {
			t.Skip("skipping consignee validation: no consignee data found")
			return
		}

		assert.NotEmpty(t, load.Consignee.City, "consignee should have city")
		assert.NotEmpty(t, load.Consignee.State, "consignee should have state")

		log.Info(ctx, "consignee info validated",
			zap.String("city", load.Consignee.City),
			zap.String("state", load.Consignee.State),
			zap.String("zipcode", load.Consignee.Zipcode),
			zap.String("timezone", load.Consignee.Timezone))
	})

	t.Run("ValidateStopsData", func(t *testing.T) {
		if len(load.Stops) == 0 {
			t.Skip("skipping stops validation: no stops data found")
			return
		}

		for i, stop := range load.Stops {
			assert.NotEmpty(t, stop.Address.City, "stop %d should have city", i+1)
			assert.NotEmpty(t, stop.StopType, "stop %d should have stop type", i+1)

			log.Info(ctx, "stop validated",
				zap.Int("stopNumber", stop.StopNumber),
				zap.String("stopType", stop.StopType),
				zap.String("city", stop.Address.City),
				zap.String("state", stop.Address.State),
				zap.String("externalTMSStopID", stop.ExternalTMSStopID))
		}
	})

	t.Run("ValidateLoadAttributes", func(t *testing.T) {
		assert.NotNil(t, attrs, "load attributes should not be nil")

		log.Info(ctx, "load attributes validated",
			zap.Bool("pickupApptStartTimeReadOnly", attrs.Pickup.ApptStartTime.IsReadOnly),
			zap.Bool("consigneeApptStartTimeReadOnly", attrs.Consignee.ApptStartTime.IsReadOnly),
			zap.Bool("carrierNameReadOnly", attrs.Carrier.Name.IsReadOnly),
			zap.Bool("statusReadOnly", attrs.Status.IsReadOnly),
			zap.Bool("externalTMSIDReadOnly", attrs.ExternalTMSID.IsReadOnly))
	})
}
