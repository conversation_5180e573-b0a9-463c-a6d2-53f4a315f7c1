package stark

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	prodRESTBaseURL = "https://app.transfix.io/stark"
	prodGraphQLURL  = "https://transfix.io/marketplace"
	prodShipmentURL = "https://apps.transfix.io/shipment-details"

	stagingRESTBaseURL = "https://staging.transfix.io/stark"
	stagingGraphQLURL  = "https://staging.transfix.io/marketplace"
	stagingShipmentURL = "https://apps-staging.transfix.io/#/shipment-details"
)

type Stark struct {
	tms            models.Integration
	restBaseURL    string
	graphQLURL     string
	userID         string // x-user-id header value
	rogersRevision string // x-rogers-revision header value
	shipmentURL    string
}

func New(_ context.Context, tms models.Integration) (*Stark, error) {
	isStaging := tms.AppID == "staging" || strings.Contains(tms.Tenant, "staging")

	var restBaseURL, graphQLURL, shipmentURL string
	if isStaging {
		restBaseURL = stagingRESTBaseURL
		graphQLURL = stagingGraphQLURL
		shipmentURL = stagingShipmentURL
	} else {
		restBaseURL = prodRESTBaseURL
		graphQLURL = prodGraphQLURL
		shipmentURL = prodShipmentURL
	}

	userID, rogersRevision := parseMetadata(tms.Note)

	return &Stark{
		tms:            tms,
		restBaseURL:    restBaseURL,
		graphQLURL:     graphQLURL,
		userID:         userID,
		rogersRevision: rogersRevision,
		shipmentURL:    shipmentURL,
	}, nil
}

func parseMetadata(note string) (userID, rogersRevision string) {
	if note == "" {
		return "", ""
	}

	parts := strings.Split(note, ",")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "user_id:") {
			userID = strings.TrimPrefix(part, "user_id:")
		} else if strings.HasPrefix(part, "rogers_revision:") {
			rogersRevision = strings.TrimPrefix(part, "rogers_revision:")
		}
	}

	return userID, rogersRevision
}

func (s *Stark) GetTestLoads() map[string]bool {
	// TODO: Add test load IDs once we have access to test environment
	return map[string]bool{}
}

func (s *Stark) GetLocations(ctx context.Context, _ ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	query := `
		query Facilities(
			$uuid: ID,
			$facilityName: String,
			$vendorName: String,
			$vendorIndustry: VendorIndustries,
			$shipperFacilityCode: String,
			$shipperName: String,
			$shipperApplicationId: ID,
			$city: String,
			$stateCode: States,
			$countryCode: Countries,
			$after: String,
			$before: String,
			$first: Int,
			$last: Int,
			$address: String,
			$reviewStatus: [FacilityReviewStatuses!],
			$ediDuplicateFacilityUuid: ID!,
			$includeEdiMatchingFacilities: Boolean!
		) {
			paginatedFacilities(
				uuid: $uuid
				name: $facilityName
				vendorName: $vendorName
				vendorIndustry: $vendorIndustry
				after: $after
				before: $before
				first: $first
				last: $last
				shipperFacilityCode: $shipperFacilityCode
				shipperName: $shipperName
				city: $city
				stateCode: $stateCode
				countryCode: $countryCode
				address: $address
				shipperApplicationId: $shipperApplicationId
				sortBy: facility_name
				reviewStatus: $reviewStatus
			) @skip(if: $includeEdiMatchingFacilities) {
				nodes {
					...Facility
					__typename
				}
				pageInfo {
					endCursor
					hasNextPage
					hasPreviousPage
					startCursor
					__typename
				}
				totalCount
				__typename
			}
			ediMatchingFacilities(
				shipperFacilityUuid: $ediDuplicateFacilityUuid
			) @include(if: $includeEdiMatchingFacilities) {
				...Facility
				__typename
			}
		}

		fragment Facility on Facility {
			id
			name
			uuid
			reviewStatus
			reviewStatusSetAt
			reviewStatusSetBy {
				id
				fullName
				__typename
			}
			contacts {
				...FacilityContact
				__typename
			}
			location {
				id
				address
				addressLine2
				city
				stateCode
				zipcode
				__typename
			}
			vendorFacilities {
				id
				vendor {
					id
					name
					__typename
				}
				__typename
			}
			shipperFacilities {
				...ShipperFacility
				__typename
			}
			__typename
		}

		fragment FacilityContact on FacilityContact {
			email
			id
			name
			phone
			phoneExtension
			website
			__typename
		}

		fragment ShipperFacility on ShipperFacility {
			id
			facilityCode
			identifiers {
				id
				facilityCode
				__typename
			}
			shipperApplication {
				id
				name
				__typename
			}
			__typename
		}
	`

	variables := map[string]any{
		"first":                        100,
		"includeEdiMatchingFacilities": false,
		"ediDuplicateFacilityUuid":     "",
	}

	var response FacilitiesGraphQLResponse
	err := s.graphQLQuery(ctx, "Facilities", variables, query, &response, s3backup.TypeLocations)
	if err != nil {
		return nil, fmt.Errorf("failed to get locations from GraphQL: %w", err)
	}

	locations := make([]models.TMSLocation, 0, len(response.Data.PaginatedFacilities.Nodes))
	for _, facility := range response.Data.PaginatedFacilities.Nodes {
		location := s.facilityToLocation(facility)
		locations = append(locations, location)
	}

	// There are more then 80K+ locations in TMS so commenting out pagination for now
	// TODO: Revisit pagination later
	// pageInfo := response.Data.PaginatedFacilities.PageInfo
	// for pageInfo.HasNextPage {
	// 	variables["after"] = pageInfo.EndCursor

	// 	var nextResponse FacilitiesGraphQLResponse
	// 	err := s.graphQLQuery(ctx, "Facilities", variables, query, &nextResponse, s3backup.TypeLocations)
	// 	if err != nil {
	// 		log.WarnNoSentry(ctx, "failed to get next page of locations", zap.Error(err))
	// 		break
	// 	}

	// 	for _, facility := range nextResponse.Data.PaginatedFacilities.Nodes {
	// 		location := s.facilityToLocation(facility)
	// 		locations = append(locations, location)
	// 	}

	// 	pageInfo = nextResponse.Data.PaginatedFacilities.PageInfo
	// }

	return locations, nil
}

func (s *Stark) facilityToLocation(facility FacilityGraphQL) models.TMSLocation {
	loc := models.TMSLocation{
		TMSIntegrationID: s.tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: facility.UUID,
			Name:          facility.Name,
			AddressLine1:  facility.Location.Address,
			City:          facility.Location.City,
			State:         facility.Location.StateCode,
			Zipcode:       facility.Location.Zipcode,
		},
	}

	if facility.Location.AddressLine2 != nil {
		loc.AddressLine2 = *facility.Location.AddressLine2
	}

	if len(facility.Contacts) > 0 {
		contact := facility.Contacts[0]
		loc.Contact = contact.Name
		loc.Phone = contact.Phone
		if contact.Email != nil {
			loc.Email = *contact.Email
		}
	}

	parts := []string{}
	if loc.AddressLine1 != "" {
		parts = append(parts, loc.AddressLine1)
	}
	if loc.AddressLine2 != "" {
		parts = append(parts, loc.AddressLine2)
	}
	if loc.City != "" {
		parts = append(parts, loc.City)
	}
	if loc.State != "" {
		parts = append(parts, loc.State)
	}
	if loc.Zipcode != "" {
		parts = append(parts, loc.Zipcode)
	}
	loc.FullAddress = strings.Join(parts, ", ")

	if loc.Name != "" {
		loc.NameAddress = loc.Name + ", " + loc.FullAddress
	} else {
		loc.NameAddress = loc.FullAddress
	}

	loc.IsShipper = len(facility.ShipperFacilities) > 0

	return loc
}

func (s *Stark) CreateLocation(
	context.Context,
	models.CreateLocationRequest,
	*models.TMSUser,
) (models.TMSLocation, error) {
	return models.TMSLocation{}, helpers.NotImplemented(models.Stark, "CreateLocation")
}

func (s *Stark) GetCarriers(ctx context.Context) ([]models.TMSCarrier, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCarriersStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	queryParams := url.Values{}
	queryParams.Set("pageCount", "0")
	queryParams.Set("pageSize", "100")

	var response CarriersResponse
	err := s.get(ctx, "/v10/carriers", queryParams, &response, s3backup.TypeCarriers)
	if err != nil {
		return nil, fmt.Errorf("failed to get carriers: %w", err)
	}

	carriers := make([]models.TMSCarrier, 0, len(response.Carriers))
	for _, carrierData := range response.Carriers {
		carrier := s.carrierToTMSCarrier(carrierData)
		carriers = append(carriers, carrier)
	}

	pageCount := 1
	for len(response.Carriers) == 100 {
		queryParams.Set("pageCount", fmt.Sprintf("%d", pageCount))
		var nextResponse CarriersResponse
		err := s.get(ctx, "/v10/carriers", queryParams, &nextResponse, s3backup.TypeCarriers)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get next page of carriers", zap.Error(err))
			break
		}

		if len(nextResponse.Carriers) == 0 {
			break
		}

		for _, carrierData := range nextResponse.Carriers {
			carrier := s.carrierToTMSCarrier(carrierData)
			carriers = append(carriers, carrier)
		}

		pageCount++
		if len(nextResponse.Carriers) < 100 {
			break
		}
	}

	return carriers, nil
}

func (s *Stark) carrierToTMSCarrier(carrier Carrier) models.TMSCarrier {
	tmsCarrier := models.TMSCarrier{
		TMSIntegrationID: s.tms.ID,
		ServiceID:        s.tms.ServiceID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: fmt.Sprintf("%d", carrier.ID),
			Name:          carrier.LegalName,
			City:          carrier.City,
			State:         carrier.StateCode,
			Zipcode:       carrier.PostalCode,
		},
		DOTNumber: carrier.DOTNumber,
	}

	if carrier.ContactName != nil {
		tmsCarrier.Contact = *carrier.ContactName
	}
	if carrier.PhoneLabel != nil {
		tmsCarrier.Phone = *carrier.PhoneLabel
	}

	if carrier.DBAName != nil && *carrier.DBAName != "" {
		if tmsCarrier.Name == "" {
			tmsCarrier.Name = *carrier.DBAName
		} else {
			tmsCarrier.Name = carrier.LegalName + " (" + *carrier.DBAName + ")"
		}
	}

	return tmsCarrier
}

func (s *Stark) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.Stark, "GetUsers")
}

func (s *Stark) GetCustomers(ctx context.Context) ([]models.TMSCustomer, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	query := `
		query Shippers(
			$after: String,
			$before: String,
			$first: Int,
			$last: Int,
			$searchTerm: String!,
			$state: ShipperApplicationStates
		) {
			searchShipperApplications(
				after: $after
				before: $before
				first: $first
				last: $last
				searchTerm: $searchTerm
				state: $state
			) {
				nodes {
					...ShipperApplication
					__typename
				}
				pageInfo {
					endCursor
					hasNextPage
					hasPreviousPage
					startCursor
					__typename
				}
				totalCount
				__typename
			}
		}

		fragment ShipperApplication on ShipperApplication {
			id
			name
			state
			shipmentEmail
			scacValue
			effectiveScac
			__typename
		}
	`

	variables := map[string]any{
		"state":      "confirmed",
		"searchTerm": "",
		"first":      100,
	}

	var response ShippersGraphQLResponse
	err := s.graphQLQuery(ctx, "Shippers", variables, query, &response, s3backup.TypeCustomers)
	if err != nil {
		return nil, fmt.Errorf("failed to get customers from GraphQL: %w", err)
	}

	customers := make([]models.TMSCustomer, 0, len(response.Data.SearchShipperApplications.Nodes))
	for _, shipper := range response.Data.SearchShipperApplications.Nodes {
		if shipper.State != "confirmed" {
			continue
		}

		customer := models.TMSCustomer{
			TMSIntegrationID: s.tms.ID,
			CompanyCoreInfo: models.CompanyCoreInfo{
				ExternalTMSID: shipper.ID,
				Name:          shipper.Name,
				Email:         shipper.ShipmentEmail,
			},
		}

		customers = append(customers, customer)
	}

	pageInfo := response.Data.SearchShipperApplications.PageInfo
	for pageInfo.HasNextPage {
		variables["after"] = pageInfo.EndCursor

		var nextResponse ShippersGraphQLResponse
		err := s.graphQLQuery(ctx, "Shippers", variables, query, &nextResponse, s3backup.TypeCustomers)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get next page of customers", zap.Error(err))
			break
		}

		for _, shipper := range nextResponse.Data.SearchShipperApplications.Nodes {
			if shipper.State != "confirmed" {
				continue
			}

			customer := models.TMSCustomer{
				TMSIntegrationID: s.tms.ID,
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: shipper.ID,
					Name:          shipper.Name,
					Email:         shipper.ShipmentEmail,
				},
			}

			customers = append(customers, customer)
		}

		pageInfo = nextResponse.Data.SearchShipperApplications.PageInfo
	}

	return customers, nil
}

func (s *Stark) CreateLoad(
	ctx context.Context,
	load models.Load,
	user *models.TMSUser,
) (models.Load, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	if load.Customer.ExternalTMSID == "" {
		return models.Load{}, errors.New("Customer.ExternalTMSID (shipperApplicationId) is required to create shipment")
	}

	// Build stops from load
	stops, err := s.buildCreateShipmentStops(ctx, load)
	if err != nil {
		return models.Load{}, fmt.Errorf("failed to build stops: %w", err)
	}

	if len(stops) == 0 {
		return models.Load{}, errors.New("at least one stop (pickup or delivery) is required")
	}

	// Map equipment type
	equipmentType := s.mapTransportTypeToStarkEquipment(
		load.Specifications.TransportType,
		load.Specifications.TransportTypeEnum,
	)

	// Build equipment attributes
	equipmentAttributes := s.buildEquipmentAttributes(load)

	// Determine rate type - default to "spot" if not specified
	rateType := "spot"
	if load.RateData.CustomerRateType != "" {
		// Map customer rate type to Stark rate type
		// TODO: Add more mappings as needed
		rateType = "spot"
	}

	// Use opsRepId from user if available, otherwise use s.userID
	opsRepID := s.userID
	if user != nil && user.ExternalTMSID != "" {
		opsRepID = user.ExternalTMSID
	}

	// Get rate value
	rate := float64(0.0)
	if load.RateData.CustomerLineHaulCharge.Val > 0 {
		rate = float64(load.RateData.CustomerLineHaulCharge.Val)
	} else if load.RateData.CustomerTotalCharge.Val > 0 {
		rate = float64(load.RateData.CustomerTotalCharge.Val)
	}

	input := map[string]any{
		"shipperApplicationId": load.Customer.ExternalTMSID,
		"equipmentType":        equipmentType,
		"equipmentAttributes":  equipmentAttributes,
		"stops":                stops,
		"totalWeight":          int(load.Specifications.TotalWeight.Val),
		"totalPalletCount":     load.Specifications.TotalInPalletCount,
		"hot":                  false, // TODO: Check if hot flag exists in load model
		"team":                 false, // TODO: Map from load if available
		"opsRepId":             opsRepID,
		"shipperRate":          rate,
		"shipperRateType":      rateType,
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation CreateShipment($input: CreateShipmentInput!) {
		createShipment(input: $input) {
			shipment {
				id
				__typename
			}
			errors {
				key
				message
				__typename
			}
			__typename
		}
	}`

	var response CreateShipmentResponse
	err = s.graphQLQuery(ctx, "CreateShipment", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, fmt.Errorf("failed to create shipment: %w", err)
	}

	if len(response.Data.CreateShipment.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.CreateShipment.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return models.Load{}, fmt.Errorf("create shipment failed: %s", strings.Join(errorMessages, ", "))
	}

	// Fetch the created shipment
	createdLoad, _, err := s.GetLoad(ctx, response.Data.CreateShipment.Shipment.ID)
	if err != nil {
		return models.Load{}, fmt.Errorf("failed to fetch created shipment: %w", err)
	}

	return createdLoad, nil
}

func (s *Stark) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) (models.Load, models.LoadAttributes, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	attrs := s.GetDefaultLoadAttributes()

	if updatedLoad.ExternalTMSID == "" {
		return models.Load{}, attrs, errors.New("ExternalTMSID is required for UpdateLoad")
	}

	// Check if basic info changed (weight, pallets, miles, characteristics)
	basicInfoChanged := s.hasBasicInfoChanges(curLoad, updatedLoad)
	if basicInfoChanged {
		err := s.updateShipmentBasicInfo(ctx, updatedLoad)
		if err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update shipment basic info: %w", err)
		}
	}

	// Check if equipment changed
	equipmentChanged := s.hasEquipmentChanges(curLoad, updatedLoad)
	if equipmentChanged {
		err := s.updateShipmentEquipment(ctx, updatedLoad)
		if err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update shipment equipment: %w", err)
		}
	}

	// Check if stops/appointments changed
	stopsChanged := s.hasStopsChanges(curLoad, updatedLoad)
	if stopsChanged {
		err := s.updateShipmentStops(ctx, updatedLoad)
		if err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update shipment stops: %w", err)
		}
	}

	if curLoad != nil {
		// Update pickup manual eta if ready time changed
		err := s.checkAndUpdateManualEta(
			ctx,
			curLoad.Pickup.ReadyTime,
			updatedLoad.Pickup.ReadyTime,
			updatedLoad.Pickup.Timezone,
			updatedLoad.Pickup.ApptStartTime,
			updatedLoad.Pickup.ExternalTMSStopID,
		)
		if err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update pickup manual eta: %w", err)
		}

		// Update consignee manual eta if must deliver time changed
		err = s.checkAndUpdateManualEta(
			ctx,
			curLoad.Consignee.MustDeliver,
			updatedLoad.Consignee.MustDeliver,
			updatedLoad.Consignee.Timezone,
			updatedLoad.Consignee.ApptStartTime,
			updatedLoad.Consignee.ExternalTMSStopID,
		)
		if err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update consignee manual eta: %w", err)
		}

		// Update Carrier timestamps (PickupStart/End, DeliveryStart/End)
		if err := s.checkAndUpdateCarrierTimestamps(ctx, curLoad, updatedLoad); err != nil {
			return models.Load{}, attrs, fmt.Errorf("failed to update stops arrival/departure: %w", err)
		}
	}

	// Fetch the updated shipment to return
	result, _, err := s.GetLoad(ctx, updatedLoad.ExternalTMSID)
	if err != nil {
		return models.Load{}, attrs, fmt.Errorf("failed to fetch updated shipment: %w", err)
	}

	return result, attrs, nil
}

func (s *Stark) hasBasicInfoChanges(curLoad, updatedLoad *models.Load) bool {
	if curLoad == nil {
		return true
	}

	// Check weight
	if updatedLoad.Specifications.TotalWeight.Val != curLoad.Specifications.TotalWeight.Val {
		return true
	}

	// Check pallet count
	if updatedLoad.Specifications.TotalOutPalletCount != curLoad.Specifications.TotalOutPalletCount {
		return true
	}

	// Check miles (shipperMiles)
	if updatedLoad.Specifications.TotalDistance.Val != curLoad.Specifications.TotalDistance.Val {
		return true
	}

	// Check characteristics (hot, mustGo)
	curHot := false
	updatedHot := false
	curMustGo := false
	updatedMustGo := false

	for _, ref := range curLoad.AdditionalReferences {
		if ref.Qualifier == "HOT" {
			curHot = true
		}
		if ref.Qualifier == "MUST_GO" {
			curMustGo = true
		}
	}

	for _, ref := range updatedLoad.AdditionalReferences {
		if ref.Qualifier == "HOT" {
			updatedHot = true
		}
		if ref.Qualifier == "MUST_GO" {
			updatedMustGo = true
		}
	}

	if curHot != updatedHot || curMustGo != updatedMustGo {
		return true
	}

	return false
}

func (s *Stark) hasEquipmentChanges(curLoad, updatedLoad *models.Load) bool {
	if curLoad == nil {
		return true
	}

	// Check transport type
	if updatedLoad.Specifications.TransportType != curLoad.Specifications.TransportType {
		return true
	}

	if updatedLoad.Specifications.TransportTypeEnum != nil && curLoad.Specifications.TransportTypeEnum != nil {
		if *updatedLoad.Specifications.TransportTypeEnum != *curLoad.Specifications.TransportTypeEnum {
			return true
		}
	} else if updatedLoad.Specifications.TransportTypeEnum != nil || curLoad.Specifications.TransportTypeEnum != nil {
		return true
	}

	return false
}

func (s *Stark) updateShipmentBasicInfo(ctx context.Context, load *models.Load) error {
	shipmentID := load.ExternalTMSID

	hot := false
	mustGo := false
	for _, ref := range load.AdditionalReferences {
		if ref.Qualifier == "HOT" {
			hot = true
		}
		if ref.Qualifier == "MUST_GO" {
			mustGo = true
		}
	}

	var declaredValue *float64
	if load.DeclaredValueUSD > 0 {
		val := float64(load.DeclaredValueUSD)
		declaredValue = &val
	}

	var shipperMiles *float64
	if load.Specifications.TotalDistance.Val > 0 {
		val := float64(load.Specifications.TotalDistance.Val)
		shipperMiles = &val
	}

	var totalWeight *int
	if load.Specifications.TotalWeight.Val > 0 {
		val := int(load.Specifications.TotalWeight.Val)
		totalWeight = &val
	}

	var totalPalletCount *int
	if load.Specifications.TotalOutPalletCount > 0 {
		val := load.Specifications.TotalOutPalletCount
		totalPalletCount = &val
	}

	input := map[string]any{
		"id":              shipmentID,
		"hot":             hot,
		"mustGo":          mustGo,
		"team":            false,
		"isBackhaul":      false,
		"fraudValidation": false,
		"isHighRisk":      false,
	}

	if declaredValue != nil {
		input["declaredValue"] = *declaredValue
	} else {
		input["declaredValue"] = nil
	}

	if shipperMiles != nil {
		input["shipperMiles"] = *shipperMiles
	}

	if totalWeight != nil {
		input["totalWeight"] = *totalWeight
	}

	if totalPalletCount != nil {
		input["totalPalletCount"] = *totalPalletCount
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation UpdateShipmentBasicInfo($input: UpdateShipmentInput!) {
		updateShipment(input: $input) {
			clientMutationId
			shipment {
				id
				calculatedMiles
				characteristics {
					bounceCount
					fraudValidation
					hasDropTrailerStops
					hot
					mustGo
					rolloverCount
					sameDaySpot
					isHighRisk
					__typename
				}
				isBackhaul
				shipperMiles
				team
				totalPalletCount
				totalWeight
				__typename
			}
			errors {
				key
				message
				__typename
			}
			__typename
		}
	}`

	var response UpdateShipmentBasicInfoResponse
	err := s.graphQLQuery(ctx, "UpdateShipmentBasicInfo", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("graphQL query failed: %w", err)
	}

	if len(response.Data.UpdateShipment.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.UpdateShipment.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("update failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func (s *Stark) updateShipmentEquipment(ctx context.Context, load *models.Load) error {
	shipmentID := load.ExternalTMSID

	// Map transport type to Stark equipment type
	equipmentType := s.mapTransportTypeToStarkEquipment(
		load.Specifications.TransportType,
		load.Specifications.TransportTypeEnum,
	)

	// Build equipment attributes (e.g., can_reefer, lift_gate)
	equipmentAttributes := []map[string]any{}

	// TODO: Map equipment attributes from load specifications
	// For now, we'll just update the equipment type

	input := map[string]any{
		"shipmentId":          shipmentID,
		"equipmentType":       equipmentType,
		"equipmentAttributes": equipmentAttributes,
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation UpdateShipmentEquipment($input: UpdateShipmentEquipmentInput!) {
		updateShipmentEquipment(input: $input) {
			clientMutationId
			errors {
				key
				message
				__typename
			}
			shipment {
				id
				equipment {
					id
					key
					name
					parentKey
					parentName
					__typename
				}
				equipmentAttributes {
					id
					attributeValue
					parent {
						id
						key
						__typename
					}
					__typename
				}
				__typename
			}
			__typename
		}
	}`

	var response UpdateShipmentEquipmentResponse
	err := s.graphQLQuery(ctx, "UpdateShipmentEquipment", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("graphQL query failed: %w", err)
	}

	if len(response.Data.UpdateShipmentEquipment.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.UpdateShipmentEquipment.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("update failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func (s *Stark) hasStopsChanges(curLoad, updatedLoad *models.Load) bool {
	if curLoad == nil {
		return true
	}

	curPickup := curLoad.Pickup
	updatedPickup := updatedLoad.Pickup

	curPickup.ReadyTime = models.NullTime{}
	updatedPickup.ReadyTime = models.NullTime{}

	// Check if pickup changed
	if len(curPickup.Diff(updatedPickup)) > 0 {
		return true
	}

	curConsignee := curLoad.Consignee
	updatedConsignee := updatedLoad.Consignee

	curConsignee.MustDeliver = models.NullTime{}
	updatedConsignee.MustDeliver = models.NullTime{}

	// Check if consignee changed
	if len(curConsignee.Diff(updatedConsignee)) > 0 {
		return true
	}

	return false
}

func (s *Stark) updateShipmentStops(ctx context.Context, load *models.Load) error {
	// First, get the current load to get stop IDs
	currentLoad, _, err := s.GetLoad(ctx, load.ExternalTMSID)
	if err != nil {
		return fmt.Errorf("failed to get current load for stop IDs: %w", err)
	}

	stops := []map[string]any{}

	// Update pickup stop if it has ExternalTMSStopID
	if load.Pickup.ExternalTMSStopID != "" {
		stopUpdate := s.buildStopUpdate(load.Pickup.ExternalTMSStopID, load.Pickup, &currentLoad.Pickup)
		if stopUpdate != nil {
			stops = append(stops, stopUpdate)
		}
	}

	// Update consignee stop if it has ExternalTMSStopID
	if load.Consignee.ExternalTMSStopID != "" {
		stopUpdate := s.buildStopUpdateConsignee(
			load.Consignee.ExternalTMSStopID,
			load.Consignee, &currentLoad.Consignee,
		)
		if stopUpdate != nil {
			stops = append(stops, stopUpdate)
		}
	}

	if len(stops) == 0 {
		return nil
	}

	input := map[string]any{
		"shipmentStops": stops,
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation UpdateShipmentStops($input: UpdateShipmentStopsInput!) {
		updateShipmentStops(input: $input) {
			errors {
				key
				message
				__typename
			}
			__typename
		}
	}`

	var response UpdateShipmentStopsResponse
	err = s.graphQLQuery(ctx, "UpdateShipmentStops", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("graphQL query failed: %w", err)
	}

	if len(response.Data.UpdateShipmentStops.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.UpdateShipmentStops.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("update stops failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func (s *Stark) buildStopUpdate(stopID string, updated models.Pickup, current *models.Pickup) map[string]any {
	stopUpdate := map[string]any{
		"id": stopID,
	}

	// Build appointment input if appointment times changed
	appointmentInput := map[string]any{}
	hasAppointmentUpdate := false

	if updated.ApptStartTime.Valid {
		appointmentInput["status"] = "confirmed"

		// Determine appointment type
		switch updated.ApptType {
		case "range", "window":
			appointmentInput["appointmentType"] = "range"
		case "appointment":
			appointmentInput["appointmentType"] = "appointment"
		default:
			appointmentInput["appointmentType"] = "appointment"
		}

		hasAppointmentUpdate = true
	}

	loc, err := time.LoadLocation(updated.Timezone)
	if err != nil {
		loc = time.UTC
	}
	if updated.ApptStartTime.Valid &&
		current != nil &&
		current.ApptStartTime.Valid &&
		current.ApptStartTime.Time != updated.ApptStartTime.Time {
		startTimeLocal := updated.ApptStartTime.Time.In(loc)

		appointmentInput["startAtDate"] = startTimeLocal.Format("2006-01-02")

		if startTimeLocal.Hour() != 0 || startTimeLocal.Minute() != 0 {
			appointmentInput["startAtTime"] = startTimeLocal.Format("15:04")
		}

		appointmentInput["rescheduleReason"] = "no_fault__first_appointment"
	}

	if updated.ApptEndTime.Valid &&
		current != nil &&
		current.ApptEndTime.Valid &&
		current.ApptEndTime.Time != updated.ApptEndTime.Time &&
		strings.ToLower(updated.ApptType) == "range" {
		endTimeLocal := updated.ApptEndTime.Time.In(loc)

		appointmentInput["endAtDate"] = endTimeLocal.Format("2006-01-02")

		if endTimeLocal.Hour() != 0 || endTimeLocal.Minute() != 0 {
			appointmentInput["endAtTime"] = endTimeLocal.Format("15:04")
		}

		appointmentInput["rescheduleReason"] = "no_fault__first_appointment"
	}

	if updated.ApptRequired {
		appointmentInput["isWorkIn"] = false
	} else {
		appointmentInput["isWorkIn"] = true
	}

	if hasAppointmentUpdate {
		stopUpdate["appointmentInput"] = appointmentInput
	}

	// Update special instruction if changed
	if updated.ApptNote != "" && (current == nil || current.ApptNote != updated.ApptNote) {
		stopUpdate["specialInstruction"] = updated.ApptNote
	}

	// Update ref number if changed
	if updated.RefNumber != "" && (current == nil || current.RefNumber != updated.RefNumber) {
		stopUpdate["poReference"] = updated.RefNumber
	}

	return stopUpdate
}

func (s *Stark) buildStopUpdateConsignee(
	stopID string,
	updated models.Consignee,
	current *models.Consignee,
) map[string]any {
	stopUpdate := map[string]any{
		"id": stopID,
	}

	// Build appointment input if appointment times changed
	appointmentInput := map[string]any{}
	hasAppointmentUpdate := false

	if updated.ApptStartTime.Valid {
		appointmentInput["status"] = "confirmed"

		// Determine appointment type
		switch updated.ApptType {
		case "range", "window":
			appointmentInput["appointmentType"] = "range"
		case "appointment":
			appointmentInput["appointmentType"] = "appointment"
		default:
			appointmentInput["appointmentType"] = "appointment"
		}

		hasAppointmentUpdate = true
	}

	loc, err := time.LoadLocation(updated.Timezone)
	if err != nil {
		loc = time.UTC
	}
	if updated.ApptStartTime.Valid &&
		current != nil &&
		current.ApptStartTime.Valid &&
		current.ApptStartTime.Time != updated.ApptStartTime.Time {
		startTimeLocal := updated.ApptStartTime.Time.In(loc)

		appointmentInput["startAtDate"] = startTimeLocal.Format("2006-01-02")

		if startTimeLocal.Hour() != 0 || startTimeLocal.Minute() != 0 {
			appointmentInput["startAtTime"] = startTimeLocal.Format("15:04")
		}

		appointmentInput["rescheduleReason"] = "no_fault__first_appointment"
	}

	if updated.ApptEndTime.Valid &&
		current != nil &&
		current.ApptEndTime.Valid &&
		current.ApptEndTime.Time != updated.ApptEndTime.Time &&
		strings.ToLower(updated.ApptType) == "range" {
		endTimeLocal := updated.ApptEndTime.Time.In(loc)

		appointmentInput["endAtDate"] = endTimeLocal.Format("2006-01-02")

		if endTimeLocal.Hour() != 0 || endTimeLocal.Minute() != 0 {
			appointmentInput["endAtTime"] = endTimeLocal.Format("15:04")
		}

		appointmentInput["rescheduleReason"] = "no_fault__first_appointment"
	}

	if updated.ApptRequired {
		appointmentInput["isWorkIn"] = false
	} else {
		appointmentInput["isWorkIn"] = true
	}

	if hasAppointmentUpdate {
		stopUpdate["appointmentInput"] = appointmentInput
	}

	// Update special instruction if changed
	if updated.ApptNote != "" && (current == nil || current.ApptNote != updated.ApptNote) {
		stopUpdate["specialInstruction"] = updated.ApptNote
	}

	// Update ref number if changed
	if updated.RefNumber != "" && (current == nil || current.RefNumber != updated.RefNumber) {
		stopUpdate["poReference"] = updated.RefNumber
	}

	return stopUpdate
}

func (s *Stark) checkAndUpdateManualEta(
	ctx context.Context,
	curTime, updatedTime models.NullTime,
	updatedTimezone string,
	apptStartTime models.NullTime,
	stopID string,
) error {
	if !updatedTime.Valid || stopID == "" {
		return nil
	}

	changed := !curTime.Valid || !curTime.Time.Equal(updatedTime.Time)
	if !changed {
		return nil
	}

	loc := time.UTC
	if updatedTimezone != "" {
		l, err := time.LoadLocation(updatedTimezone)
		if err == nil {
			loc = l
		}
	}
	etaLocal := updatedTime.Time.In(loc)

	lateReason := ""
	// If updated time is later than appointment start time, mark as late
	if apptStartTime.Valid && updatedTime.Time.After(apptStartTime.Time) {
		lateReason = "Other"
	}

	return s.updateShipmentStopManualEta(ctx, stopID, etaLocal, lateReason)
}

func (s *Stark) updateShipmentStopManualEta(
	ctx context.Context,
	stopID string,
	eta time.Time,
	lateReason string,
) error {
	input := map[string]any{
		"shipmentStopId": stopID,
		"manualEtaDate":  eta.Format("2006-01-02"),
		"manualEtaTime":  eta.Format("15:04"),
	}

	if lateReason != "" {
		input["lateReason"] = lateReason
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation UpdateShipmentStopManualEta($input: UpdateShipmentStopManualEtaInput!) {
	  updateShipmentStopManualEta(input: $input) {
		errors {
		  key
		  message
		  __typename
		}
		shipmentStop {
		  id
		  manualEta
		  __typename
		}
		__typename
	  }
	}`

	var response LoadUpdateGraphQLResponse
	err := s.graphQLQuery(ctx, "UpdateShipmentStopManualEta", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("graphQL query failed: %w", err)
	}

	if len(response.Data.UpdateShipmentStopManualEta.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.UpdateShipmentStopManualEta.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("update failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func (s *Stark) checkAndUpdateCarrierTimestamps(
	ctx context.Context,
	curLoad *models.Load,
	updatedLoad *models.Load,
) error {
	var carrierUpdates []map[string]any

	// Pickup Stop Update
	if updatedLoad.Pickup.ExternalTMSStopID != "" {
		pickupStartChanged := isNullTimeChanged(curLoad.Carrier.PickupStart, updatedLoad.Carrier.PickupStart)
		pickupEndChanged := isNullTimeChanged(curLoad.Carrier.PickupEnd, updatedLoad.Carrier.PickupEnd)

		if pickupStartChanged || pickupEndChanged {
			update := map[string]any{
				"shipmentStopId": updatedLoad.Pickup.ExternalTMSStopID,
			}

			// Add ArrivedAt (PickupStart)
			addDateTimesToUpdate(update, "arrivedAt", updatedLoad.Carrier.PickupStart, updatedLoad.Pickup.Timezone)

			// Add LeftAt (PickupEnd)
			addDateTimesToUpdate(update, "leftAt", updatedLoad.Carrier.PickupEnd, updatedLoad.Pickup.Timezone)

			carrierUpdates = append(carrierUpdates, update)
		}
	}

	// Delivery Stop Update
	if updatedLoad.Consignee.ExternalTMSStopID != "" {
		deliveryStartChanged := isNullTimeChanged(curLoad.Carrier.DeliveryStart, updatedLoad.Carrier.DeliveryStart)
		deliveryEndChanged := isNullTimeChanged(curLoad.Carrier.DeliveryEnd, updatedLoad.Carrier.DeliveryEnd)

		if deliveryStartChanged || deliveryEndChanged {
			update := map[string]any{
				"shipmentStopId": updatedLoad.Consignee.ExternalTMSStopID,
			}

			// Add ArrivedAt (DeliveryStart)
			addDateTimesToUpdate(update, "arrivedAt", updatedLoad.Carrier.DeliveryStart, updatedLoad.Consignee.Timezone)

			// Add LeftAt (DeliveryEnd)
			addDateTimesToUpdate(update, "leftAt", updatedLoad.Carrier.DeliveryEnd, updatedLoad.Consignee.Timezone)

			carrierUpdates = append(carrierUpdates, update)
		}
	}

	if len(carrierUpdates) > 0 {
		return s.updateShipmentStopsArrivalDeparture(ctx, carrierUpdates)
	}

	return nil
}

func (s *Stark) updateShipmentStopsArrivalDeparture(ctx context.Context, updates []map[string]any) error {
	if len(updates) == 0 {
		return nil
	}
	input := map[string]any{
		"updates": updates,
	}
	variables := map[string]any{
		"input": input,
	}

	query := `mutation UpdateShipmentStopsArrivalDeparture($input: UpdateShipmentStopsArrivalDepartureInput!) {
	  updateShipmentStopsArrivalDeparture(input: $input) {
		errors {
		  key
		  message
		  __typename
		}
		shipment {
		  id
		  state
		  statusDetails {
			detailedState
			pickupStatus
			__typename
		  }
		  __typename
		}
		__typename
	  }
	}`
	var response UpdateShipmentStopsArrivalDepartureResponse
	err := s.graphQLQuery(ctx, "UpdateShipmentStopsArrivalDeparture", variables, query, &response, s3backup.TypeLoads)
	if err != nil {
		return fmt.Errorf("graphQL query failed: %w", err)
	}
	if len(response.Data.UpdateShipmentStopsArrivalDeparture.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.UpdateShipmentStopsArrivalDeparture.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("update failed: %s", strings.Join(errorMessages, ", "))
	}
	return nil
}

func isNullTimeChanged(t1, t2 models.NullTime) bool {
	if !t1.Valid && !t2.Valid {
		return false
	}
	if t1.Valid != t2.Valid {
		return true
	}
	return !t1.Time.Equal(t2.Time)
}

func addDateTimesToUpdate(update map[string]any, prefix string, t models.NullTime, timezone string) {
	if !t.Valid {
		return
	}

	loc := time.UTC
	if timezone != "" {
		l, err := time.LoadLocation(timezone)
		if err == nil {
			loc = l
		}
	}
	localTime := t.Time.In(loc)

	update[prefix+"Date"] = localTime.Format("2006-01-02")
	update[prefix+"Time"] = localTime.Format("15:04")
}

func (s *Stark) buildCreateShipmentStops(_ context.Context, load models.Load) ([]map[string]any, error) {
	if load.Pickup.ExternalTMSID == "" && load.Consignee.ExternalTMSID == "" && len(load.Stops) == 0 {
		return nil, errors.New("at least one stop (pickup or delivery) is required")
	}

	stops := []map[string]any{}
	stopSequence := 1

	// Add pickup stop
	if load.Pickup.ExternalTMSID != "" {
		pickupStop := s.buildCreateStop(load.Pickup, load.Commodities, "pickup", stopSequence)
		if pickupStop != nil {
			stops = append(stops, pickupStop)
			stopSequence++
		}
	}

	// Add delivery stop
	if load.Consignee.ExternalTMSID != "" {
		deliveryStop := s.buildCreateStopFromConsignee(load.Consignee, load.Commodities, "delivery", stopSequence)
		if deliveryStop != nil {
			stops = append(stops, deliveryStop)
			stopSequence++
		}
	}

	// Add additional stops if present
	for _, stop := range load.Stops {
		if stop.ExternalTMSID != "" {
			stopType := "delivery"
			if stop.StopType == "pickup" {
				stopType = "pickup"
			}
			additionalStop := s.buildCreateStopFromStop(stop, stopType, stopSequence)
			if additionalStop != nil {
				stops = append(stops, additionalStop)
				stopSequence++
			}
		}
	}

	return stops, nil
}

func (s *Stark) buildCreateStop(
	pickup models.Pickup,
	commodities []models.Commodity,
	stopType string,
	sequence int,
) map[string]any {
	stop := map[string]any{
		"stopType":     stopType,
		"stopSequence": sequence,
		"dropTrailer":  false,
	}

	// Get commodity from first commodity if available, otherwise use Specifications.Commodities
	commodity := ""
	if len(commodities) > 0 && commodities[0].Description != "" {
		commodity = commodities[0].Description
	}
	stop["commodity"] = commodity

	// Use ExternalTMSID as shipperFacilityUuid if it's a UUID format
	// Otherwise, we might need to look it up
	if pickup.ExternalTMSID != "" {
		stop["shipperFacilityUuid"] = pickup.ExternalTMSID
	}

	// Build appointment input
	appointmentInput := map[string]any{}
	hasAppointment := false

	if pickup.ApptStartTime.Valid {
		appointmentInput["startAtDate"] = pickup.ApptStartTime.Time.Format("2006-01-02")
		if pickup.ApptStartTime.Time.Hour() != 0 || pickup.ApptStartTime.Time.Minute() != 0 {
			appointmentInput["startAtTime"] = pickup.ApptStartTime.Time.Format("15:04")
		}
		hasAppointment = true
	}

	if pickup.ApptType == "range" || pickup.ApptType == "window" {
		appointmentInput["appointmentType"] = "range"
	} else {
		appointmentInput["appointmentType"] = "appointment"
	}

	if pickup.ApptConfirmed {
		appointmentInput["status"] = "confirmed"
	} else {
		appointmentInput["status"] = "unconfirmed"
	}

	if hasAppointment {
		stop["appointmentInput"] = appointmentInput
	}

	return stop
}

func (s *Stark) buildCreateStopFromConsignee(
	consignee models.Consignee,
	commodities []models.Commodity,
	stopType string,
	sequence int,
) map[string]any {
	stop := map[string]any{
		"stopType":     stopType,
		"stopSequence": sequence,
		"dropTrailer":  false,
	}

	// Get commodity from first commodity if available
	commodity := ""
	if len(commodities) > 0 && commodities[0].Description != "" {
		commodity = commodities[0].Description
	}
	stop["commodity"] = commodity

	if consignee.ExternalTMSID != "" {
		stop["shipperFacilityUuid"] = consignee.ExternalTMSID
	}

	// Build appointment input
	appointmentInput := map[string]any{}
	hasAppointment := false

	if consignee.ApptStartTime.Valid {
		appointmentInput["startAtDate"] = consignee.ApptStartTime.Time.Format("2006-01-02")
		if consignee.ApptStartTime.Time.Hour() != 0 || consignee.ApptStartTime.Time.Minute() != 0 {
			appointmentInput["startAtTime"] = consignee.ApptStartTime.Time.Format("15:04")
		}
		hasAppointment = true
	}

	if consignee.ApptType == "range" || consignee.ApptType == "window" {
		appointmentInput["appointmentType"] = "range"
	} else {
		appointmentInput["appointmentType"] = "appointment"
	}

	if consignee.ApptConfirmed {
		appointmentInput["status"] = "confirmed"
	} else {
		appointmentInput["status"] = "unconfirmed"
	}

	if hasAppointment {
		stop["appointmentInput"] = appointmentInput
	}

	return stop
}

func (s *Stark) buildCreateStopFromStop(stop models.Stop, stopType string, sequence int) map[string]any {
	stopMap := map[string]any{
		"stopType":     stopType,
		"stopSequence": sequence,
		"dropTrailer":  false,
	}

	// Commodity might be in AdditionalReferences or we can leave it empty
	stopMap["commodity"] = ""

	if stop.ExternalTMSID != "" {
		stopMap["shipperFacilityUuid"] = stop.ExternalTMSID
	}

	// Build appointment input
	appointmentInput := map[string]any{}
	hasAppointment := false

	if stop.ApptStartTime.Valid {
		appointmentInput["startAtDate"] = stop.ApptStartTime.Time.Format("2006-01-02")
		if stop.ApptStartTime.Time.Hour() != 0 || stop.ApptStartTime.Time.Minute() != 0 {
			appointmentInput["startAtTime"] = stop.ApptStartTime.Time.Format("15:04")
		}
		hasAppointment = true
	}

	if stop.ApptType == "range" || stop.ApptType == "window" {
		appointmentInput["appointmentType"] = "range"
	} else {
		appointmentInput["appointmentType"] = "appointment"
	}

	if stop.ApptRequired && stop.ApptStartTime.Valid {
		appointmentInput["status"] = "confirmed"
	} else {
		appointmentInput["status"] = "unconfirmed"
	}

	if hasAppointment {
		stopMap["appointmentInput"] = appointmentInput
	}

	return stopMap
}

func (s *Stark) buildEquipmentAttributes(_ models.Load) []map[string]any {
	attributes := []map[string]any{}

	// Add equipment-specific attributes based on transport type
	// TODO: Add temp control attributes if TempControl field exists in Specifications
	// For now, return empty attributes array

	return attributes
}

func (s *Stark) mapTransportTypeToStarkEquipment(transportType string, transportTypeEnum *models.TransportType) string {
	// Map Drumkit transport types to Stark equipment keys
	if transportType != "" {
		transportTypeLower := strings.ToLower(transportType)
		switch {
		case strings.Contains(transportTypeLower, "van") || strings.Contains(transportTypeLower, "dry"):
			return "_53_van"
		case strings.Contains(transportTypeLower, "flatbed"):
			return "_53_flatbed"
		case strings.Contains(transportTypeLower, "reefer") || strings.Contains(transportTypeLower, "refrigerated"):
			return "_53_reefer"
		default:
			return "_53_van" // Default
		}
	} else if transportTypeEnum != nil {
		switch *transportTypeEnum {
		case models.VanTransportType:
			return "_53_van"
		case models.FlatbedTransportType:
			return "flatbed"
		case models.ReeferTransportType:
			return "_53_reefer"
		}
	}

	return "_53_van"
}

func (s *Stark) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	checkCall models.CheckCall,
) error {
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	if !checkCall.NextStopETAWithoutTimezone.Valid {
		return errors.New("check call must include NextStopETA to update manual ETA in Stark")
	}

	stopID := checkCall.NextStopID
	if stopID == "" {
		//nolint:gocritic
		if load.Consignee.ExternalTMSStopID != "" {
			stopID = load.Consignee.ExternalTMSStopID
		} else if load.Pickup.ExternalTMSStopID != "" {
			stopID = load.Pickup.ExternalTMSStopID
		} else {
			return errors.New(
				"nextStopID is required to update manual ETA in Stark, or load must have ExternalTMSStopID set",
			)
		}
	}

	etaTime := checkCall.NextStopETAWithoutTimezone.Time
	manualEtaDate := etaTime.Format("2006-01-02T15:04:05.000Z")
	manualEtaTime := etaTime.Format("15:04")

	lateReason := checkCall.Reason
	if lateReason == "" {
		lateReason = checkCall.Notes
	}

	query := `mutation LoadUpdate($eta: UpdateShipmentStopManualEtaInput!) {
		updateShipmentStopManualEta(input: $eta) {
			errors {
				key
				message
				__typename
			}
			shipmentStop {
				...ShipmentStop
				__typename
			}
			__typename
		}
	}

	fragment ShipmentStop on ShipmentStop {
		id
		city
		stateCode
		specialInstruction
		commodity
		dropTrailer
		note
		lateReason
		project44Eta
		manualEta
		arrivedAt
		latitude
		longitude
		leftAt
		stopSequence
		stopType
		zipcode
		appointment {
			id
			timezone
			startAt
			endAt
			appointmentType
			__typename
		}
		__typename
	}`

	variables := map[string]any{
		"eta": map[string]any{
			"manualEtaDate":  manualEtaDate,
			"manualEtaTime":  manualEtaTime,
			"shipmentStopId": stopID,
		},
	}

	if lateReason != "" {
		etaMap := variables["eta"].(map[string]any)
		etaMap["lateReason"] = lateReason
	}

	var response LoadUpdateGraphQLResponse
	err := s.graphQLQuery(ctx, "LoadUpdate", variables, query, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return fmt.Errorf("failed to update manual ETA: %w", err)
	}

	if len(response.Data.UpdateShipmentStopManualEta.Errors) > 0 {
		errorMessages := make([]string, 0, len(response.Data.UpdateShipmentStopManualEta.Errors))
		for _, gqlErr := range response.Data.UpdateShipmentStopManualEta.Errors {
			errorMessages = append(errorMessages, gqlErr.Message)
		}
		return fmt.Errorf("GraphQL errors: %s", strings.Join(errorMessages, "; "))
	}

	return nil
}

func (s *Stark) PostException(
	ctx context.Context,
	load *models.Load,
	exception models.Exception,
) error {
	ctx, metaSpan := otel.StartSpan(ctx, "PostExceptionStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	if load.ExternalTMSID == "" {
		return errors.New("ExternalTMSID is required to post exception")
	}

	// Map exception to shipment issue
	// Default issue type ID - "34" is "Changes to Shipment" based on the example
	// In a real implementation, we might want to map EventCode to different issue types
	issueTypeID := "34"
	// if exception.EventCode != "" {
	// 	// TODO: Map EventCode to appropriate issue type ID
	// 	// For now, use default
	// }

	// Build owner - use current user if available
	owner := map[string]any{
		"followerType": "User",
	}

	// Convert userID string to int for followerId
	if s.userID != "" {
		userIDInt, err := strconv.Atoi(s.userID)
		if err == nil {
			owner["followerId"] = userIDInt
		} else {
			// If conversion fails, try to use as-is (might be numeric string)
			owner["followerId"] = s.userID
		}
	}

	// Build subscribers - empty for now, could be populated from exception fields
	subscribers := []map[string]any{}

	input := map[string]any{
		"shipmentId":          load.ExternalTMSID,
		"shipmentIssueTypeId": issueTypeID,
		"owner":               owner,
		"subscribers":         subscribers,
		"state":               "pending",
	}

	// Add summary if note is provided
	if exception.Note != "" {
		input["summary"] = exception.Note
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation CreateShipmentIssue($input: CreateShipmentIssueInput!) {
		createShipmentIssue(input: $input) {
			errors {
				key
				message
				__typename
			}
			shipmentIssue {
				id
				__typename
			}
			__typename
		}
	}`

	var response CreateShipmentIssueResponse
	err := s.graphQLQuery(ctx, "CreateShipmentIssue", variables, query, &response, s3backup.TypeExceptions)
	if err != nil {
		return fmt.Errorf("failed to create shipment issue: %w", err)
	}

	if len(response.Data.CreateShipmentIssue.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.CreateShipmentIssue.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return fmt.Errorf("create exception failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func (s *Stark) PostNote(
	ctx context.Context,
	load *models.Load,
	note models.Note,
) ([]models.Note, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "PostNoteStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	if load.ExternalTMSID == "" {
		return nil, errors.New("ExternalTMSID is required to post note")
	}

	if note.Note == "" && note.Text == "" {
		return nil, errors.New("note text is required")
	}

	noteText := note.Note
	if noteText == "" {
		noteText = note.Text
	}

	// Create note using CreateNote mutation
	input := map[string]any{
		"noteableId":   load.ExternalTMSID,
		"noteableType": "Shipment",
		"body":         noteText,
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation CreateNote($input: CreateNoteInput!) {
		createNote(input: $input) {
			clientMutationId
			errors {
				key
				message
				__typename
			}
			note {
				id
				...NoteInfo
				__typename
			}
			__typename
		}
	}

	fragment NoteInfo on Note {
		id
		body
		callBackAt
		calledBackAt
		createdAt
		createdBy {
			firstName
			lastName
			id
			parentUserId
			userType
			__typename
		}
		createdById
		documents {
			name
			url
			__typename
		}
		__typename
	}`

	var response CreateNoteResponse
	err := s.graphQLQuery(ctx, "CreateNote", variables, query, &response, s3backup.TypeNotes)
	if err != nil {
		return nil, fmt.Errorf("failed to create note: %w", err)
	}

	if len(response.Data.CreateNote.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.CreateNote.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return nil, fmt.Errorf("create note failed: %s", strings.Join(errorMessages, ", "))
	}

	// Fetch all notes to return the complete list
	return s.getShipmentNotes(ctx, load.ExternalTMSID)
}

func (s *Stark) CreateQuote(context.Context, models.CreateQuoteBody) (*models.CreateQuoteResponse, error) {
	return nil, helpers.NotImplemented(models.Stark, "CreateQuote")
}

func (s *Stark) GetOrder(context.Context, string) (*models.Order, models.OrderAttributes, error) {
	return nil, models.OrderAttributes{}, helpers.NotImplemented(models.Stark, "GetOrder")
}

func (s *Stark) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) ([]models.Load, models.LoadAttributes, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	if idType != ShipmentIDType {
		return nil, models.LoadAttributes{}, fmt.Errorf("unsupported ID type: %s", idType)
	}

	load, attrs, err := s.GetLoad(ctx, id)
	if err != nil {
		return nil, attrs, err
	}

	return []models.Load{load}, attrs, nil
}

func (s *Stark) MapTransportTypeEnum(tmsTransportType string) (models.TransportType, error) {
	switch tmsTransportType {
	case "dry_van", "53' Van", "Van":
		return models.VanTransportType, nil
	case "flatbed", "Flatbed":
		return models.FlatbedTransportType, nil
	case "reefer", "Refrigerated":
		return models.ReeferTransportType, nil
	case "hotshot", "Hotshot":
		return models.HotShotTransportType, nil
	case "box_truck", "Box Truck":
		return models.BoxTruckTransportType, nil
	case "sprinter", "Sprinter":
		return models.SprinterTransportType, nil
	default:
		return models.SpecialTransportType, nil
	}
}

func (s *Stark) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	dataType s3backup.DataType,
) error {
	parsedURL, err := url.Parse(s.restBaseURL)
	if err != nil {
		return fmt.Errorf("failed to parse REST base URL: %w", err)
	}

	pathToUse := path
	if parsedURL.Path != "" && parsedURL.Path != "/" {
		pathToUse = parsedURL.Path + path
	}

	addr := url.URL{
		Scheme: parsedURL.Scheme,
		Host:   parsedURL.Host,
		Path:   pathToUse,
	}
	if queryParams != nil {
		addr.RawQuery = queryParams.Encode()
	}

	headerMap := s.buildHeaders()

	// authorization, err := s.getAuthToken(ctx)
	// if err != nil {
	// 	return fmt.Errorf("failed to get auth token: %w", err)
	// }

	body, _, err := httputil.GetBytesWithToken(ctx, s.tms, addr, headerMap, nil, dataType)
	if err != nil {
		return err
	}

	return json.Unmarshal(body, dst)
}

// func (s *Stark) getAuthToken(ctx context.Context) (*string, error) {
// 	if s.tms.AccessToken != "" && time.Now().Before(s.tms.AccessTokenExpirationDate.Time) {
// 		auth := fmt.Sprintf("Bearer %s", s.tms.AccessToken)
// 		return &auth, nil
// 	}

// 	if err := s.RefreshToken(ctx); err != nil {
// 		return nil, fmt.Errorf("failed to refresh token: %w", err)
// 	}

// 	auth := fmt.Sprintf("Bearer %s", s.tms.AccessToken)
// 	return &auth, nil
// }

func (s *Stark) buildHeaders() map[string]string {
	headerMap := make(map[string]string)

	if s.tms.APIKey != "" {
		headerMap["x-user-token"] = s.tms.APIKey
	}
	if s.tms.Username != "" {
		headerMap["x-user-email"] = s.tms.Username
	}
	if s.userID != "" {
		headerMap["x-user-id"] = s.userID
	} else {
		headerMap["x-user-id"] = "839816"
	}
	if s.rogersRevision != "" {
		headerMap["x-rogers-revision"] = s.rogersRevision
	} else {
		headerMap["x-rogers-revision"] = "202511251414-70e06f4ef293df9c439b35abf3d96a1df433f8a4"
	}

	headerMap["content-type"] = "application/json"
	headerMap["accept"] = "*/*"
	headerMap["x-camel-case"] = "true"

	return headerMap
}

func (s *Stark) graphQLQuery(
	ctx context.Context,
	operationName string,
	variables map[string]any,
	query string,
	dst any,
	dataType s3backup.DataType,
) error {
	queryParams := url.Values{}
	queryParams.Set("op", operationName)

	parsedURL, err := url.Parse(s.graphQLURL)
	if err != nil {
		return fmt.Errorf("failed to parse GraphQL URL: %w", err)
	}

	addr := url.URL{
		Scheme:   parsedURL.Scheme,
		Host:     parsedURL.Host,
		Path:     parsedURL.Path,
		RawQuery: queryParams.Encode(),
	}

	reqBody := GraphQLRequest{
		OperationName: operationName,
		Variables:     variables,
		Query:         query,
	}

	headerMap := s.buildHeaders()

	// authorization, err := s.getAuthToken(ctx)
	// if err != nil {
	// 	return fmt.Errorf("failed to get auth token: %w", err)
	// }

	body, _, err := httputil.PostBytesWithToken(ctx, s.tms, addr, reqBody, headerMap, nil, dataType)
	if err != nil {
		return err
	}

	return json.Unmarshal(body, dst)
}

func (s *Stark) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	return models.OnboardTMSResponse{
		Username: onboardRequest.Username,
		Tenant:   onboardRequest.Tenant,
		APIKey:   onboardRequest.APIKey,
	}, nil
}
