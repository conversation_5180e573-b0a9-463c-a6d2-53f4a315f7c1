package stark

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// GetLoad retrieves a single shipment by ID using GraphQL
func (s *Stark) GetLoad(
	ctx context.Context,
	externalTMSID string,
) (load models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := otel.IntegrationAttrs(s.tms)

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadStark", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = s.GetDefaultLoadAttributes()

	shipmentQuery := `query ShipmentDetailsStatus($id: ID!) {
		shipment(id: $id) {
			...ShipmentBasicInfo
			id
			tmsShipmentPublicUuid
			links {
				starkCopyFmsShipmentUrl
				__typename
			}
			state
			specialInstruction
			hasClaims
			hasOsd
			shipperApplication {
				id
				carrierInstructions
				__typename
			}
			trackingOverview {
				trackingState
				calculatedTrackingMethod
				__typename
			}
			rateConfirmationAgreement {
				id
				carrierSignature
				carrierSignedAt
				__typename
			}
			statusDetails {
				detailedState
				pickupStatus
				__typename
			}
			__typename
		}
	}

	fragment ShipmentBasicInfo on Shipment {
		id
		shipperShipmentReference
		shipperMiles
		totalWeight
		totalPalletCount
		calculatedMiles
		bolReferences
		poReferences
		loadingType
		isBackhaul
		team
		firstPickup {
			...ShipmentStop
			__typename
		}
		lastDelivery {
			...ShipmentStop
			__typename
		}
		stops {
			...ShipmentStop
			__typename
		}
		equipment {
			id
			name
			parentKey
			parentName
			__typename
		}
		characteristics {
			hot
			mustGo
			haltClaimsBilling
			haltClaimsInvoicing
			__typename
		}
		shipperTrackingSegment {
			id
			description
			level
			__typename
		}
		__typename
	}

	fragment ShipmentStop on ShipmentStop {
		id
		city
		stateCode
		specialInstruction
		commodity
		dropTrailer
		note
		lateReason
		project44Eta
		manualEta
		arrivedAt
		latitude
		longitude
		leftAt
		stopSequence
		stopType
		zipcode
		appointment {
			id
			timezone
			startAt
			endAt
			appointmentType
			__typename
		}
		__typename
	}`

	customerQuery := `query ShipmentCustomerInfo($id: ID!) {
		shipment(id: $id) {
			id
			shipperRate
			shipperLineHaulAndFuelRate
			shipperPartnerReference
			shipperTrackingSegment {
				id
				description
				level
				__typename
			}
			cxRep {
				id
				parentUserId
				fullName
				__typename
			}
			currentCxTask {
				id
				completedAt
				__typename
			}
			shipperApplication {
				id
				state
				segment
				name
				opsCustomerEmail
				cohortYear
				cohortQuarter
				isFsmaApproved
				accountManager {
					id
					parentUserId
					fullName
					__typename
				}
				carrierInstructions
				podId
				shipperRelationships {
					...ShipperRelationshipInfo
					__typename
				}
				groups {
					id
					name
					key
					__typename
				}
				__typename
			}
			pricingType
			shipperRateSources {
				id
				createdAt
				difference
				rate {
					id
					amount
					__typename
				}
				rateTypeKey
				updatedAt
				__typename
			}
			__typename
		}
	}

	fragment ShipperRelationshipInfo on ShipperRelationship {
		id
		user {
			id
			fullName
			parentUserId
			__typename
		}
		relationshipType
		relationshipLevel
		__typename
	}`

	carrierQuery := `query ShipmentCarrierInfo($id: ID!) {
		shipment(id: $id) {
			id
			state
			truckNumber
			trailerNumber
			truckNumberRequired
			trailerNumberRequired
			carrierRate
			carrierLineHaulAndFuelRate
			contactPreferences {
				carrierContactInstructions
				__typename
			}
			characteristics {
				fraudValidation
				__typename
			}
			driver {
				id
				__typename
			}
			dispatchedAt
			vin
			vinVerified
			firstPickup {
				...ShipmentStopBasicInfo
				__typename
			}
			lastDelivery {
				...ShipmentStopBasicInfo
				__typename
			}
			...CarrierInfo
			__typename
		}
		}

		fragment ShipmentStopBasicInfo on ShipmentStop {
			id
			city
			dropTrailer
			stateCode
			zipcode
			__typename
		}

		fragment CarrierInfo on Shipment {
			id
			carrierApplicationId
			activeCarrierAssignment {
				id
				trackingMethod
				driver {
					...CarrierInfoUser
					__typename
				}
				dispatcher {
					...CarrierInfoUser
					__typename
				}
				carrierApplication {
					...CarrierApplicationData
					__typename
				}
				__typename
			}
			carrierManager {
				id
				parentUserId
				fullName
				__typename
			}
			coveredBid {
				id
				amount
				carrierAvailableTruck {
					id
					trailerNumber
					truckNumber
					vin
					isVinConfirmed
					__typename
				}
				carrierApplication {
					...CarrierApplicationData
					__typename
				}
				carrierContactName
				carrierContactPhone
				carrierContactPhoneExt
				carrierContactUserId
				carrierContactUser {
					...CarrierInfoUser
					__typename
				}
				driver {
					...CarrierInfoUser
					__typename
				}
				__typename
			}
			currentCeRep {
				id
				parentUserId
				fullName
				__typename
			}
			rateConfirmationAgreement {
				id
				carrierSignedAt
				__typename
			}
			__typename
		}

		fragment CarrierInfoUser on User {
			id
			fullName
			parentUserId
			email
			phone
			phoneExt
			cellPhone
			__typename
		}

		fragment CarrierApplicationData on CarrierApplication {
			id
			name
			dotNumber
			mcNumber
			state
			fmcsaRating
			carrierSpecificInfo
			isScheduledAuto
			activeShipments {
				totalCount
				__typename
			}
			headquarter {
				id
				city
				stateCode
				__typename
			}
			primaryCarrierManager {
				id
				parentUserId
				fullName
				__typename
			}
			carrierRelationships {
				id
				relationshipLevel
				relationshipType
				user {
				id
				fullName
				parentUserId
				__typename
				}
				__typename
			}
			__typename
		}`

	variables := map[string]any{
		"id": externalTMSID,
	}

	var shipmentResponse ShipmentDetailsStatusGraphQLResponse
	var customerResponse ShipmentCustomerInfoGraphQLResponse
	var carrierResponse ShipmentCarrierInfoGraphQLResponse

	g, gctx := errgroup.WithContext(ctx)

	// ---- Query 1: Shipment Info ----
	g.Go(func() error {
		return s.graphQLQuery(
			gctx,
			"ShipmentDetailsStatus",
			variables,
			shipmentQuery,
			&shipmentResponse,
			s3backup.TypeLoads,
		)
	})

	// ---- Query 2: Customer Info ----
	g.Go(func() error {
		return s.graphQLQuery(
			gctx,
			"ShipmentCustomerInfo",
			variables,
			customerQuery,
			&customerResponse,
			s3backup.TypeLoads,
		)
	})

	// ---- Query 3: Carrier Info ----
	g.Go(func() error {
		return s.graphQLQuery(
			gctx,
			"ShipmentCarrierInfo",
			variables,
			carrierQuery,
			&carrierResponse,
			s3backup.TypeLoads,
		)
	})

	var notes []models.Note
	g.Go(func() error {
		notes, err = s.getShipmentNotes(gctx, externalTMSID)
		return err
	})

	// Wait for all 3 goroutines to finish
	if err = g.Wait(); err != nil {
		return load, attrs, fmt.Errorf("GraphQL parallel query failed: %w", err)
	}

	if shipmentResponse.Data.Shipment.ID == "" {
		return load, attrs, errtypes.EntityNotFoundError(s.tms, externalTMSID, "shipment_id")
	}

	load, err = s.shipmentDetailsToLoad(
		ctx,
		shipmentResponse.Data.Shipment,
		customerResponse.Data.Shipment,
		carrierResponse.Data.Shipment,
		notes,
	)
	if err != nil {
		return load, attrs, fmt.Errorf("failed to map shipment to load: %w", err)
	}

	return load, attrs, nil
}

// GetLoadIDs retrieves shipment IDs based on query parameters
func (s *Stark) GetLoadIDs(
	ctx context.Context,
	queryParams models.SearchLoadsQuery,
) ([]string, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(nil) }()

	// Build filters based on query parameters
	filters := s.buildFilters(queryParams)

	filtersJSON, err := json.Marshal(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filters: %w", err)
	}

	allIDs := make([]string, 0)
	pageCount := 0

	for {
		apiQueryParams := url.Values{}
		apiQueryParams.Set("filters", string(filtersJSON))
		apiQueryParams.Set("sort_field", "pickups")
		apiQueryParams.Set("sort_order", "asc")
		apiQueryParams.Set("page_count", strconv.Itoa(pageCount))

		var response ShipmentResponse
		err = s.get(ctx, "/v8/shipments", apiQueryParams, &response, s3backup.TypeLoads)
		if err != nil {
			if pageCount == 0 {
				return nil, fmt.Errorf("failed to get shipments: %w", err)
			}
			// If we've already collected some results, return what we have
			log.WarnNoSentry(ctx, "failed to get next page of shipments, returning partial results",
				zap.Error(err),
				zap.Int("pageCount", pageCount),
				zap.Int("collectedSoFar", len(allIDs)))
			break
		}

		if len(response.Shipments) == 0 {
			break
		}

		for _, shipment := range response.Shipments {
			allIDs = append(allIDs, strconv.FormatInt(shipment.ID, 10))
		}

		pageCount++
	}

	return allIDs, nil
}

func (s *Stark) buildFilters(query models.SearchLoadsQuery) map[string]any {
	filters := make(map[string]any)

	filters["pod_ids"] = []any{}
	filters["includes"] = []string{"open_issues"}
	filters["users_id"] = nil
	filters["users_type"] = nil
	filters["equipment"] = map[string]any{}
	filters["pickup_city"] = ""
	filters["pickup_state_code"] = ""
	filters["pickup_regions"] = ""
	filters["delivery_city"] = ""
	filters["delivery_state_code"] = ""
	filters["delivery_regions"] = ""
	filters["pickup_date"] = ""
	filters["pickup_end_date"] = ""
	filters["pricing_type"] = ""
	filters["delivery_date"] = ""
	filters["delivery_end_date"] = ""
	filters["query"] = ""
	filters["pickup_miles"] = ""
	filters["delivery_miles"] = ""
	filters["has_hot"] = false
	filters["has_rolled"] = false
	filters["no_appointment_time"] = false
	filters["unconfirmed_appointment"] = false
	filters["has_claims"] = false
	filters["multi_day"] = false
	filters["state_options"] = ""
	filters["after_hours"] = false
	filters["drop_trailer"] = false
	filters["broker_pod_key"] = nil
	filters["needs_tracking"] = false
	filters["needs_compliance_review"] = false
	filters["in_compliance_review"] = false
	filters["shipper_rate_type"] = []any{}
	filters["must_go"] = false

	if query.FromFreightTrackingID != "" {
		filters["query"] = query.FromFreightTrackingID
	}

	if query.Status != "" {
		switch strings.ToLower(query.Status) {
		case "tendered":
			filters["status"] = []string{"tendered"}
		case "waterfall_offered":
			filters["status"] = []string{"waterfall_offered"}
		case "alerted":
			filters["status"] = []string{"alerted"}
		case "reserved":
			filters["status"] = []string{"reserved"}
		default:
			filters["status"] = []string{query.Status}
		}
	}

	if query.FromDate.Valid {
		filters["pickup_date"] = query.FromDate.Time.Format("2006-01-02")
	}
	if query.ToDate.Valid {
		filters["pickup_end_date"] = query.ToDate.Time.Format("2006-01-02")
	}

	if query.Pickup.City != "" {
		filters["pickup_city"] = query.Pickup.City
	}
	if query.Pickup.State != "" {
		filters["pickup_state_code"] = query.Pickup.State
	}
	if query.Dropoff.City != "" {
		filters["delivery_city"] = query.Dropoff.City
	}
	if query.Dropoff.State != "" {
		filters["delivery_state_code"] = query.Dropoff.State
	}

	return filters
}

func parseTimeNullable(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{Valid: false}
	}
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return models.NullTime{Valid: false}
	}
	return models.NullTime{Time: t, Valid: true}
}

func mapAppointmentType(apptType string) string {
	switch strings.ToLower(apptType) {
	case "appt", "appointment":
		return "appointment"
	case "window", "range":
		return "range"
	default:
		return "appointment"
	}
}

func (s *Stark) shipmentDetailsToLoad(
	_ context.Context,
	shipment ShipmentDetailsGraphQL,
	customerInfo ShipmentCustomerInfoGraphQL,
	carrierInfo ShipmentCarrierInfoGraphQL,
	notes []models.Note,
) (models.Load, error) {
	if shipment.ID == "" {
		return models.Load{}, errors.New("shipment ID is empty")
	}
	load := models.Load{
		TMSID:             s.tms.ID,
		ServiceID:         s.tms.ServiceID,
		ExternalTMSID:     shipment.ID,
		FreightTrackingID: shipment.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: shipment.StatusDetails.DetailedState,
		},
	}

	load.Customer = models.Customer{
		CompanyCoreInfo: models.CompanyCoreInfo{},
	}

	load.Carrier = models.Carrier{}
	load.RateData = models.RateData{}

	if len(shipment.BOLReferences) > 0 {
		for _, bol := range shipment.BOLReferences {
			load.AdditionalReferences = append(
				load.AdditionalReferences,
				models.AdditionalReference{
					Qualifier: "BOL",
					Number:    bol,
				},
			)
		}
	}
	if len(shipment.POReferences) > 0 {
		load.PONums = strings.Join(shipment.POReferences, ",")
	}

	if shipment.FirstPickup != nil {
		if shipment.FirstPickup.Commodity != "" {
			load.Specifications.Commodities = shipment.FirstPickup.Commodity
		}
		load.Pickup = s.stopGraphQLToPickup(*shipment.FirstPickup)
		if shipment.FirstPickup.ArrivedAt != nil {
			load.Carrier.PickupStart = parseTimeNullable(*shipment.FirstPickup.ArrivedAt)
		}
		if shipment.FirstPickup.LeftAt != nil {
			load.Carrier.PickupEnd = parseTimeNullable(*shipment.FirstPickup.LeftAt)
		}
		if shipment.FirstPickup.City != "" {
			load.Carrier.DispatchCity = shipment.FirstPickup.City
		}
		if shipment.FirstPickup.StateCode != "" {
			load.Carrier.DispatchState = shipment.FirstPickup.StateCode
		}
	}

	if shipment.LastDelivery != nil {
		load.Consignee = s.stopGraphQLToConsignee(*shipment.LastDelivery)
		if shipment.LastDelivery.ArrivedAt != nil {
			load.Carrier.DeliveryStart = parseTimeNullable(*shipment.LastDelivery.ArrivedAt)
		}
		if shipment.LastDelivery.LeftAt != nil {
			load.Carrier.DeliveryEnd = parseTimeNullable(*shipment.LastDelivery.LeftAt)
		}
	}

	if shipment.RateConfirmationAgreement != nil && shipment.RateConfirmationAgreement.CarrierSignature != "" {
		load.Carrier.SignedBy = shipment.RateConfirmationAgreement.CarrierSignature
		load.Carrier.RateConfirmationSent = true
		load.Carrier.ConfirmationSentTime = parseTimeNullable(shipment.RateConfirmationAgreement.CarrierSignedAt)
	}

	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(shipment.TotalWeight),
		Unit: models.LbsUnit,
	}
	load.Specifications.TotalOutPalletCount = shipment.TotalPalletCount
	if shipment.CalculatedMiles > 0 {
		load.Specifications.TotalDistance = models.ValueUnit{
			Val:  float32(math.Round(shipment.CalculatedMiles*100) / 100),
			Unit: models.MilesUnit,
		}
	} else if shipment.ShipperMiles > 0 {
		load.Specifications.TotalDistance = models.ValueUnit{
			Val:  float32(math.Round(shipment.ShipperMiles*100) / 100),
			Unit: models.MilesUnit,
		}
	}

	// Map equipment
	if shipment.Equipment.Name != "" {
		load.Specifications.TransportType = shipment.Equipment.Name
		transportType, err := s.MapTransportTypeEnum(shipment.Equipment.Name)
		if err == nil {
			load.Specifications.TransportTypeEnum = &transportType
		}
	}

	if shipment.Characteristics.Hot {
		load.AdditionalReferences = append(
			load.AdditionalReferences,
			models.AdditionalReference{
				Qualifier: "HOT",
				Number:    "",
			},
		)
	}
	if shipment.Characteristics.MustGo {
		load.AdditionalReferences = append(
			load.AdditionalReferences,
			models.AdditionalReference{
				Qualifier: "MUST_GO",
				Number:    "",
			},
		)
	}

	if len(notes) > 0 {
		load.Notes = notes
	}

	if shipment.ShipperApplication.ID != "" {
		load.Customer.ExternalTMSID = shipment.ShipperApplication.ID
		load.Carrier.ExternalTMSID = shipment.ShipperApplication.ID

		if len(notes) == 0 && shipment.ShipperApplication.CarrierInstructions != "" {
			var text string
			if shipment.SpecialInstruction != nil {
				text = *shipment.SpecialInstruction
			}
			load.Notes = append(load.Notes, models.Note{
				Note:          shipment.ShipperApplication.CarrierInstructions,
				ExternalTMSID: shipment.ID,
				Text:          text,
			})
		}
	}

	if len(notes) == 0 && shipment.SpecialInstruction != nil && *shipment.SpecialInstruction != "" {
		load.Notes = append(load.Notes, models.Note{
			Note:          *shipment.SpecialInstruction,
			ExternalTMSID: shipment.ID,
			Text:          *shipment.SpecialInstruction,
		})

	}

	// Populate stops array for multi-stop support
	if len(shipment.Stops) > 0 {
		load.Stops = make([]models.Stop, len(shipment.Stops))
		for i, stop := range shipment.Stops {
			load.Stops[i] = s.stopGraphQLToStop(stop)
		}
		if len(shipment.Stops) > 2 {
			load.MoreThanTwoStops = true
		}
	}

	if customerInfo.ShipperApplication != nil {
		load.Customer.Name = customerInfo.ShipperApplication.Name
		load.Customer.Email = customerInfo.ShipperApplication.OpsCustomerEmail
	}

	if shipment.ShipperShipmentReference != nil {
		load.Customer.RefNumber = *shipment.ShipperShipmentReference
	}

	if carrierInfo.ActiveCarrierAssignment != nil {
		if carrierInfo.ActiveCarrierAssignment.CarrierApplication != nil {
			load.Carrier.Name = carrierInfo.ActiveCarrierAssignment.CarrierApplication.Name
		}

		if carrierInfo.ActiveCarrierAssignment.Driver != nil {
			load.Carrier.FirstDriverName = carrierInfo.ActiveCarrierAssignment.Driver.FullName
			load.Carrier.FirstDriverPhone = carrierInfo.ActiveCarrierAssignment.Driver.CellPhone
		}

		if carrierInfo.ActiveCarrierAssignment.Dispatcher != nil {
			load.Carrier.Dispatcher = carrierInfo.ActiveCarrierAssignment.Dispatcher.FullName
		}
	}

	if carrierInfo.TrailerNumber != nil {
		load.Carrier.ExternalTMSTrailerID = *carrierInfo.TrailerNumber
	}
	if carrierInfo.TruckNumber != nil {
		load.Carrier.ExternalTMSTruckID = *carrierInfo.TruckNumber
	}

	if carrierInfo.CarrierLineHaulAndFuelRate != nil {
		load.RateData.CarrierLineHaulCharge.Val = float32(*carrierInfo.CarrierLineHaulAndFuelRate)
		load.RateData.CarrierLineHaulCharge.Unit = "USD"
	}

	if customerInfo.ShipperLineHaulAndFuelRate != nil {
		load.RateData.CustomerLineHaulCharge.Val = float32(*customerInfo.ShipperLineHaulAndFuelRate)
		load.RateData.CustomerLineHaulCharge.Unit = "USD"
	}

	load.URL = fmt.Sprintf("%s/%s", s.shipmentURL, shipment.ID)
	return load, nil
}

func (s *Stark) stopGraphQLToPickup(stop ShipmentStopGraphQL) models.Pickup {
	pickup := models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		ExternalTMSStopID: stop.ID,
		RefNumber:         "",
		Timezone:          "America/New_York",
	}

	if stop.Note != nil {
		pickup.RefNumber = *stop.Note
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		pickup.Timezone = stop.Appointment.Timezone
		if stop.Appointment.Timezone == "" {
			pickup.Timezone = "America/New_York"
		}
		pickup.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			pickup.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		pickup.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
	}

	if stop.SpecialInstruction != nil && *stop.SpecialInstruction != "" {
		pickup.ApptNote = *stop.SpecialInstruction
	}

	if stop.ManualEta != "" {
		pickup.ReadyTime = parseTimeNullable(stop.ManualEta)
	}

	return pickup
}

func (s *Stark) stopGraphQLToConsignee(stop ShipmentStopGraphQL) models.Consignee {
	consignee := models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			City:    stop.City,
			State:   stop.StateCode,
			Zipcode: stop.Zipcode,
		},
		ExternalTMSStopID: stop.ID,
		RefNumber:         "",
		Timezone:          "America/New_York",
	}

	if stop.Note != nil {
		consignee.RefNumber = *stop.Note
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		consignee.Timezone = stop.Appointment.Timezone
		if stop.Appointment.Timezone == "" {
			consignee.Timezone = "America/New_York"
		}
		consignee.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			consignee.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		consignee.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
	}

	if stop.SpecialInstruction != nil && *stop.SpecialInstruction != "" {
		consignee.ApptNote = *stop.SpecialInstruction
	}

	if stop.ManualEta != "" {
		consignee.MustDeliver = parseTimeNullable(stop.ManualEta)
	}

	return consignee
}

func (s *Stark) stopGraphQLToStop(stop ShipmentStopGraphQL) models.Stop {
	stopModel := models.Stop{
		StopType:          stop.StopType,
		StopNumber:        stop.StopSequence - 1, // Convert to 0-based indexing
		ExternalTMSStopID: stop.ID,
		Address: models.Address{
			City:  stop.City,
			State: stop.StateCode,
			Zip:   stop.Zipcode,
		},
		Timezone: "America/New_York",
	}

	if stop.Note != nil {
		stopModel.RefNumber = *stop.Note
	}

	if stop.SpecialInstruction != nil {
		stopModel.ApptNote = *stop.SpecialInstruction
	}

	if stop.Commodity != "" {
		if stopModel.ApptNote != "" {
			stopModel.ApptNote += "\nCommodity: " + stop.Commodity
		} else {
			stopModel.ApptNote = "Commodity: " + stop.Commodity
		}
	}

	if stop.LateReason != nil && *stop.LateReason != "" {
		if stopModel.ApptNote != "" {
			stopModel.ApptNote += "\nLate Reason: " + *stop.LateReason
		} else {
			stopModel.ApptNote = "Late Reason: " + *stop.LateReason
		}
	}

	// Map appointment
	if stop.Appointment.ID != "" {
		stopModel.ApptNum = stop.Appointment.ID
		stopModel.Timezone = stop.Appointment.Timezone
		if stopModel.Timezone == "" {
			stopModel.Timezone = "America/New_York"
		}
		stopModel.ApptStartTime = parseTimeNullable(stop.Appointment.StartAt)
		if stop.Appointment.EndAt != nil {
			stopModel.ApptEndTime = parseTimeNullable(*stop.Appointment.EndAt)
		}
		stopModel.ApptType = mapAppointmentType(stop.Appointment.AppointmentType)
		stopModel.ApptRequired = stop.Appointment.AppointmentType != ""
	}

	// Map actual times
	if stop.ArrivedAt != nil {
		stopModel.ActualStartTime = parseTimeNullable(*stop.ArrivedAt)
	}
	if stop.LeftAt != nil {
		stopModel.ActualEndTime = parseTimeNullable(*stop.LeftAt)
	}

	// Map ETA times
	if stop.ManualEta != "" {
		if stop.StopType == "pickup" {
			stopModel.ReadyTime = parseTimeNullable(stop.ManualEta)
		} else {
			stopModel.MustDeliver = parseTimeNullable(stop.ManualEta)
		}
	}

	return stopModel
}
