package stark

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) getShipmentNotes(ctx context.Context, shipmentID string) ([]models.Note, error) {
	variables := map[string]any{
		"id":          shipmentID,
		"noteType":    "human",
		"isShipments": true,
	}

	query := `
		query LoadNotes(
			$id: ID!,
			$noteType: NoteTypes,
			$isShipments: Boolean!
		) {
			shipment(id: $id) @include(if: $isShipments) {
				id
				notes(noteType: $noteType, includeIssueNotes: true) {
					...NoteInfo
					__typename
				}
				__typename
			}
		}

		fragment NoteInfo on Note {
			id
			body
			callBackAt
			calledBackAt
			createdAt
			createdBy {
				firstName
				lastName
				id
				parentUserId
				userType
				__typename
			}
			createdById
			documents {
				name
				url
				__typename
			}
			__typename
		}
	`

	var response LoadNotesResponse
	err := s.graphQLQuery(ctx, "LoadNotes", variables, query, &response, s3backup.TypeNotes)
	if err != nil {
		return nil, fmt.Errorf("failed to get notes: %w", err)
	}

	notes := make([]models.Note, 0, len(response.Data.Shipment.Notes))
	for _, noteData := range response.Data.Shipment.Notes {
		note := models.Note{
			ExternalTMSID: noteData.ID,
			Text:          noteData.Body,
			Note:          noteData.Body,
		}

		if noteData.CreatedAt != "" {
			createdAt, err := time.Parse(time.RFC3339, noteData.CreatedAt)
			if err == nil {
				note.CreatedAt = models.NullTime{Time: createdAt, Valid: true}
			}
		}

		if noteData.CreatedBy.FirstName != "" || noteData.CreatedBy.LastName != "" {
			note.UpdatedBy = strings.TrimSpace(noteData.CreatedBy.FirstName + " " + noteData.CreatedBy.LastName)
		}

		if len(noteData.Documents) > 0 {
			note.IsAttachment = true
		}

		notes = append(notes, note)
	}

	return notes, nil
}
